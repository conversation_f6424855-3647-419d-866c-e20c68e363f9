import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import pandas as pd
import os

def scrape_safetyprompts():
    """
    Scrapes dataset information (name, website, and description) from safetyprompts.com
    using Selenium to handle dynamically loaded content with explicit waits.

    Returns:
        list: A list of dictionaries, where each dictionary contains the
              information for a single dataset. Returns an empty list if
              the request fails or no datasets are found.
    """
    # The URL of the website to scrape
    url = "https://safetyprompts.com/"
    # Base URL to construct absolute links if necessary
    base_url = "https://safetyprompts.com"
    datasets = []

    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 在无头模式下运行 (不打开浏览器窗口)
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    # --- 使用您提供的 User-Agent ---
    chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")

    driver = None
    try:
        # Automatically download and manage chromedriver
        print("正在设置并启动浏览器驱动...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)

        # Navigate to the page
        driver.get(url)

        # --- 使用显式等待替换固定的 time.sleep ---
        # 等待数据集卡片加载出来，最长等待20秒
        print("正在等待页面动态内容加载，请稍候...")
        wait = WebDriverWait(driver, 20)
        # 我们等待所有class以'group'开头的<a>元素出现
        # 使用CSS选择器 'a[class^="group"]'
        wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, 'a[class^="group"]')))
        print("页面内容加载完毕。")

        # Get the page source after JavaScript has executed
        page_source = driver.page_source

        # Parse the HTML content of the page with BeautifulSoup
        soup = BeautifulSoup(page_source, 'html.parser')

        # Find all dataset cards.
        dataset_cards = soup.find_all('a', class_=lambda c: c and c.startswith('group'))
        
        if not dataset_cards:
            print("警告: 未能找到任何数据集卡片。网站结构可能已再次更改。")
            return []

        # Loop through each found dataset card to extract its details
        for card in dataset_cards:
            name_tag = card.find('h3')
            name = name_tag.text.strip() if name_tag else "No Name Found"

            link = card.get('href', "No Link Found")
            if link.startswith('/'):
                link = base_url + link

            description_tag = card.find('p')
            description = description_tag.text.strip() if description_tag else "No Description Found"

            datasets.append({
                "数据集名称": name,
                "网站链接": link,
                "简介": description
            })

    except Exception as e:
        print(f"抓取过程中发生错误: {e}")
    finally:
        # Make sure to close the browser
        if driver:
            driver.quit()
            print("浏览器驱动已关闭。")

    return datasets

if __name__ == "__main__":
    # Run the scraper function
    scraped_data = scrape_safetyprompts()

    # Check if data was successfully scraped
    if scraped_data:
        print(f"\n成功抓取到 {len(scraped_data)} 个数据集。\n")
        df = pd.DataFrame(scraped_data)
        print("抓取到的数据集信息：")
        print(df.to_string())

        try:
            excel_filename = "safetyprompts_datasets.xlsx"
            df.to_excel(excel_filename, index=False)
            print(f"\n数据已成功保存到 {excel_filename} 文件。")
        except Exception as e:
            print(f"\n保存文件时出错: {e}")
    else:
        print("\n未能抓取到任何数据。请检查网络连接或网站结构。")
